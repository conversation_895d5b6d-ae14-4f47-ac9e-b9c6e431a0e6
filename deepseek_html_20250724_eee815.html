<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مصنع الزخرف للحديد | AL-Zakhrof factory</title>
    <script src="tailwind"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="css2.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
        #about-slider, #documents-slider, #accreditation-slider {
    display: flex;
    transition: transform 0.5s ease;
}

#about-slider > div, #documents-slider > div, #accreditation-slider > div {
    flex-shrink: 0;
    width: 100%;
}
/* تأكد من أن السلايدر والشرائح تأخذ الأبعاد الصحيحة */
#about-slider, #documents-slider, #accreditation-slider {
    display: flex;
    width: 100%;
    height: 600px; /* أو أي ارتفاع مناسب */
    overflow: hidden;
}

/* تأكد من أن كل شريحة تأخذ المساحة الكاملة */
#about-slider > div, 
#documents-slider > div, 
#accreditation-slider > div {
    min-width: 100%;
    flex-shrink: 0;
    height: 100%;
}

/* تأكد من أن الصور تملأ الشرائح بشكل صحيح */
#about-slider img, 
#documents-slider img, 
#accreditation-slider img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* لضمان تغطية المساحة دون تشويه */
}


    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- شريط التنقل -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2 space-x-reverse">
                <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <img src="img/logo.png">
                </div>
                <h1 class="text-xl font-bold text-gray-800">AL-Zakhrof factory</h1>
            </div>
            
            <div class="hidden md:flex items-center space-x-6 space-x-reverse">
                <a href="#about" class="text-gray-700 hover:text-blue-600 transition">من نحن</a>
                <a href="#documents" class="text-gray-700 hover:text-blue-600 transition">الوثائق</a>
                <a href="#accreditation" class="text-gray-700 hover:text-blue-600 transition">الاعتمادات</a>
                <a href="#projects" class="text-gray-700 hover:text-blue-600 transition">مشاريعنا</a>
                <a href="#contact" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">اتصل بنا</a>
            </div>
            
            <!-- زر القائمة للموبايل -->
            <button id="mobile-menu-button" class="md:hidden text-gray-700">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>
        
        <!-- القائمة المتنقلة للموبايل -->
        <div id="mobile-menu" class="hidden md:hidden bg-white py-2 px-4 shadow-lg">
            <a href="#about" class="block py-2 text-gray-700 hover:text-blue-600">من نحن</a>
            <a href="#documents" class="block py-2 text-gray-700 hover:text-blue-600">الوثائق</a>
            <a href="#accreditation" class="block py-2 text-gray-700 hover:text-blue-600">الاعتمادات</a>
            <a href="#projects" class="block py-2 text-gray-700 hover:text-blue-600">مشاريعنا</a>
            <a href="#contact" class="block py-2 text-blue-600 font-medium">اتصل بنا</a>
        </div>
    </nav>

    <!-- القسم الرئيسي -->
    <main>
        <!-- قسم من نحن -->
        <section id="about" class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">من نحن</h2>
                
                <div class="relative overflow-hidden rounded-xl shadow-lg">
                    <div id="about-slider" class="flex transition-transform duration-500">
                        <!-- سيتم إضافة الصور ديناميكيًا عبر JavaScript -->
                    </div>
                    
                    <button id="about-prev" class="absolute left-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition z-10">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button id="about-next" class="absolute right-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition z-10">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2 space-x-reverse">
                        <!-- نقاط التوجيه -->
                    </div>
                </div>
                
                <div class="mt-8 text-center max-w-3xl mx-auto">
                    <p class="text-lg text-gray-600 leading-relaxed">
                        مصنع الزخرف للحديد هو أحد الرواد في مجال صناعة الحديد والمنتجات المعدنية في المنطقة. 
                        بتأسيسنا منذ أكثر من 20 عام، كنا وما زلنا نلتزم بأعلى معايير الجودة والابتكار 
                        في تقديم حلول معدنية متكاملة تلبي احتياجات العملاء في مختلف القطاعات.
                    </p>
                </div>
            </div>
        </section>
        
        <!-- قسم الوثائق -->
        <section id="documents" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">الوثائق الرسمية</h2>
                
                <div class="relative overflow-hidden rounded-xl shadow-lg">
                    <div id="documents-slider" class="flex transition-transform duration-500">
                        <!-- سيتم إضافة الصور ديناميكيًا عبر JavaScript -->
                        <div class="w-full flex-shrink-0 bg-gray-200 h-[600px] flex items-center justify-center">
                            <span class="text-gray-500">صورة وثيقة 1 - سيتم استبدالها</span>
                        </div>
                    </div>
                    
                    <button id="documents-prev" class="absolute left-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button id="documents-next" class="absolute right-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2 space-x-reverse">
                        <!-- نقاط التوجيه -->
                    </div>
                </div>
                
                <div class="mt-8 text-center max-w-3xl mx-auto">
                    <p class="text-lg text-gray-600 leading-relaxed">
                        نفتخر بامتلاكنا جميع الوثائق والتراخيص الرسمية التي تؤهلنا لممارسة نشاطنا الصناعي 
                        وفق لأعلى المعايير المحلية والدولية. هذه الوثائق تشهد على التزامنا بالجودة والسلامة 
                        والمواصفات القياسية في جميع منتجاتنا.
                    </p>
                </div>
            </div>
        </section>
        
        <!-- قسم الاعتمادات -->
        <section id="accreditation" class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">الاعتمادات الرسمية</h2>
                
                <div class="relative overflow-hidden rounded-xl shadow-lg">
                    <div id="accreditation-slider" class="flex transition-transform duration-500">
                        <!-- سيتم إضافة الصور ديناميكيًا عبر JavaScript -->
                        <div class="w-full flex-shrink-0 bg-gray-200 h-[600px] flex items-center justify-center">
                            <span class="text-gray-500">صورة اعتماد 1 - سيتم استبدالها</span>
                        </div>
                    </div>
                    
                    <button id="accreditation-prev" class="absolute left-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button id="accreditation-next" class="absolute right-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2 space-x-reverse">
                        <!-- نقاط التوجيه -->
                    </div>
                </div>
                
                <div class="mt-8 text-center max-w-3xl mx-auto">
                    <p class="text-lg text-gray-600 leading-relaxed">
                        حصل مصنع الزخرف للحديد على العديد من الاعتمادات والشهادات المحلية والدولية التي تثبت جودة منتجاتنا 
                        والتزامنا بمعايير الصناعة العالمية. هذه الاعتمادات تمثل ثقة الجهات الرقابية والجهات المانحة 
                        في قدرتنا على تقديم منتجات تلبي توقعات العملاء.
                    </p>
                </div>
            </div>
        </section>
        
        <!-- قسم مشاريعنا -->
        <section id="projects" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">مشاريعنا</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- المشروع 1 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="h-48 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">صورة المشروع 1</span>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">مشروع ناطحة سحاب الرياض</h3>
                            <p class="text-gray-600">تزويد الهياكل الحديدية لناطحة سحاب في منطقة الأعمال المركزية بالرياض</p>
                        </div>
                    </div>
                    
                    <!-- المشروع 2 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="h-48 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">صورة المشروع 2</span>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">جسر الملك فهد</h3>
                            <p class="text-gray-600">تصنيع وتوريد الهياكل الحديدية لجسر الملك فهد في المنطقة الشرقية</p>
                        </div>
                    </div>
                    
                    <!-- المشروع 3 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="h-48 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">صورة المشروع 3</span>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">مركز تسوق الحمراء</h3>
                            <p class="text-gray-600">تصميم وتصنيع الهياكل المعدنية لمركز تسوق الحمراء في جدة</p>
                        </div>
                    </div>
                    
                    <!-- المشروع 4 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="h-64 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">صورة المشروع 4</span>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">مصنع بتروكيماويات الجبيل</h3>
                            <p class="text-gray-600">توريد الهياكل الحديدية لمصنع بتروكيماويات في مدينة الجبيل الصناعية</p>
                        </div>
                    </div>
                    
                    <!-- المشروع 5 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="h-64 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">صورة المشروع 5</span>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">مطار الأمير محمد بن عبدالعزيز</h3>
                            <p class="text-gray-600">تصنيع الهياكل المعدنية لمشروع توسعة مطار الأمير محمد بن عبدالعزيز</p>
                        </div>
                    </div>
                    
                    <!-- المشروع 6 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="h-64 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">صورة المشروع 6</span>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">فندق الريتز كارلتون</h3>
                            <p class="text-gray-600">تصميم وتصنيع الهياكل الحديدية للواجهات المعدنية لفندق الريتز كارلتون</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- قسم اتصل بنا -->
        <section id="contact" class="py-16 bg-blue-600 text-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">اتصل بنا</h2>
                
                <div class="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4">معلومات التواصل</h3>
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3 space-x-reverse">
                                <i class="fas fa-map-marker-alt mt-1"></i>
                                <p>المملكة العربية السعودية، الرياض، الصناعية الثانية</p>
                            </div>
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <i class="fas fa-phone"></i>
                                <p>+966 11 123 4567</p>
                            </div>
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <i class="fas fa-envelope"></i>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        
                        <h3 class="text-xl font-bold mt-8 mb-4">ساعات العمل</h3>
                        <div class="space-y-2">
                            <p>الأحد - الخميس: 8:00 ص - 5:00 م</p>
                            <p>الجمعة - السبت: مغلق</p>
                        </div>
                    </div>
                    
                    <div>
                        <form class="space-y-4">
                            <div>
                                <label for="name" class="block mb-1">الاسم</label>
                                <input type="text" id="name" class="w-full px-4 py-2 rounded-lg bg-blue-500 bg-opacity-20 border border-blue-400 focus:outline-none focus:ring-2 focus:ring-white">
                            </div>
                            <div>
                                <label for="email" class="block mb-1">البريد الإلكتروني</label>
                                <input type="email" id="email" class="w-full px-4 py-2 rounded-lg bg-blue-500 bg-opacity-20 border border-blue-400 focus:outline-none focus:ring-2 focus:ring-white">
                            </div>
                            <div>
                                <label for="phone" class="block mb-1">الهاتف</label>
                                <input type="tel" id="phone" class="w-full px-4 py-2 rounded-lg bg-blue-500 bg-opacity-20 border border-blue-400 focus:outline-none focus:ring-2 focus:ring-white">
                            </div>
                            <div>
                                <label for="message" class="block mb-1">الرسالة</label>
                                <textarea id="message" rows="4" class="w-full px-4 py-2 rounded-lg bg-blue-500 bg-opacity-20 border border-blue-400 focus:outline-none focus:ring-2 focus:ring-white"></textarea>
                            </div>
                            <button type="submit" class="bg-white text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition">إرسال</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- التذييل -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                            <span class="text-gray-700 font-bold">Z</span>
                        </div>
                        <h2 class="text-xl font-bold">AL-Zakhrof factory</h2>
                    </div>
                    <p class="mt-2 text-gray-400">روادة في صناعة الحديد منذ 1995</p>
                </div>
                
                <div class="flex space-x-4 space-x-reverse">
                    <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-blue-600 transition">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-blue-400 transition">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-red-600 transition">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-blue-500 transition">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2023 مصنع الزخرف للحديد. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- نموذج الصورة الكاملة -->
    <div id="image-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center hidden">
        <button id="close-modal" class="absolute top-4 left-4 text-white text-2xl">
            <i class="fas fa-times"></i>
        </button>
        
        <div class="relative w-full h-full flex items-center justify-center">
            <img id="modal-image" src="" alt="" class="max-w-full max-h-full">
            
            <button id="modal-prev" class="absolute left-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-70 transition">
                <i class="fas fa-chevron-right text-xl"></i>
            </button>
            <button id="modal-next" class="absolute right-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-70 transition">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
        </div>
    </div>

  /<script>
document.addEventListener('DOMContentLoaded', function () {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');

  mobileMenuButton.addEventListener('click', function () {
    mobileMenu.classList.toggle('hidden');
  });

  const mobileLinks = mobileMenu.querySelectorAll('a');
  mobileLinks.forEach(link => {
    link.addEventListener('click', () => {
      mobileMenu.classList.add('hidden');
    });
  });

  function createSlider(config) {
    const { containerId, prevId, nextId, images, type } = config;

    const container = document.getElementById(containerId);
    const prev = document.getElementById(prevId);
    const next = document.getElementById(nextId);

    let currentSlide = 0;

    function goToSlide(index) {
      currentSlide = index;
      container.style.transform = `translateX(-${currentSlide * 100}%)`;
      container.style.transition = 'transform 0.5s ease';

      const dotsContainer = container.parentElement.querySelector('.absolute.bottom-4');
      if (dotsContainer) {
        const dots = dotsContainer.querySelectorAll('button');
        dots.forEach((dot, i) => {
          dot.className = `w-3 h-3 rounded-full mx-1 ${i === currentSlide ? 'bg-white' : 'bg-gray-400'}`;
        });
      }
    }

    function renderSlider() {
      container.innerHTML = '';
      images.forEach((image, index) => {
        const slide = document.createElement('div');
        slide.className = 'w-full flex-shrink-0 h-[600px] flex items-center justify-center cursor-pointer';
        slide.style.minWidth = '100%';

        const imgElement = document.createElement('img');
        imgElement.src = image.src;
        imgElement.alt = image.alt;
        imgElement.className = 'w-full h-full object-cover';

        imgElement.onerror = function () {
          slide.innerHTML = `<span class="text-gray-500">${image.placeholder || image.alt}</span>`;
        };

        slide.addEventListener('click', () => openFullscreenSlider(type, index));
        slide.appendChild(imgElement);
        container.appendChild(slide);
      });

      container.style.width = `${images.length * 100}%`;
      container.style.display = 'flex';

      const dotsContainer = container.parentElement.querySelector('.absolute.bottom-4');
      if (dotsContainer) {
        dotsContainer.innerHTML = '';
        images.forEach((_, index) => {
          const dot = document.createElement('button');
          dot.className = `w-3 h-3 rounded-full mx-1 ${index === currentSlide ? 'bg-white' : 'bg-gray-400'}`;
          dot.addEventListener('click', () => goToSlide(index));
          dotsContainer.appendChild(dot);
        });
      }

      goToSlide(currentSlide);
    }

    prev.addEventListener('click', () => {
      currentSlide = (currentSlide - 1 + images.length) % images.length;
      goToSlide(currentSlide);
    });

    next.addEventListener('click', () => {
      currentSlide = (currentSlide + 1) % images.length;
      goToSlide(currentSlide);
    });

    renderSlider();

    if (type === 'about') {
      let interval = setInterval(() => {
        currentSlide = (currentSlide + 1) % images.length;
        goToSlide(currentSlide);
      }, 5000);

      container.addEventListener('mouseenter', () => clearInterval(interval));
      container.addEventListener('mouseleave', () => {
        interval = setInterval(() => {
          currentSlide = (currentSlide + 1) % images.length;
          goToSlide(currentSlide);
        }, 5000);
      });
    }
  }

  const aboutImages = [
    { src: "https://picsum.photos/800/600?random=1", alt: "صورة المصنع الخارجية" },
    { src: "https://picsum.photos/800/600?random=2", alt: "آلات الإنتاج" },
    { src: "https://picsum.photos/800/600?random=3", alt: "فريق العمل" },
    { src: "https://picsum.photos/800/600?random=4", alt: "منتجاتنا" },
    { src: "https://picsum.photos/800/600?random=5", alt: "مركز الجودة" }
  ];

  const documentsImages = [
    { src: "https://picsum.photos/800/600?random=6", alt: "وثيقة 1", placeholder: 'وثيقة 1 - سيتم استبدالها' },
    { src: "https://picsum.photos/800/600?random=7", alt: "وثيقة 2", placeholder: 'وثيقة 2 - سيتم استبدالها' },
    { src: "https://picsum.photos/800/600?random=8", alt: "وثيقة 3", placeholder: 'وثيقة 3 - سيتم استبدالها' }
  ];

  const accreditationImages = [
    { src: "https://picsum.photos/800/600?random=9", alt: "اعتماد 1", placeholder: 'اعتماد 1 - سيتم استبدالها' },
    { src: "https://picsum.photos/800/600?random=10", alt: "اعتماد 2", placeholder: 'اعتماد 2 - سيتم استبدالها' },
    { src: "https://picsum.photos/800/600?random=11", alt: "اعتماد 3", placeholder: 'اعتماد 3 - سيتم استبدالها' },
    { src: "https://picsum.photos/800/600?random=12", alt: "اعتماد 4", placeholder: 'اعتماد 4 - سيتم استبدالها' }
  ];

  createSlider({ containerId: 'about-slider', prevId: 'about-prev', nextId: 'about-next', images: aboutImages, type: 'about' });
  createSlider({ containerId: 'documents-slider', prevId: 'documents-prev', nextId: 'documents-next', images: documentsImages, type: 'documents' });
  createSlider({ containerId: 'accreditation-slider', prevId: 'accreditation-prev', nextId: 'accreditation-next', images: accreditationImages, type: 'accreditation' });

  const imageModal = document.getElementById('image-modal');
  const modalImage = document.getElementById('modal-image');
  const closeModal = document.getElementById('close-modal');
  const modalPrev = document.getElementById('modal-prev');
  const modalNext = document.getElementById('modal-next');

  let currentModalSlider = '';
  let currentModalIndex = 0;

  function openFullscreenSlider(sliderType, index) {
    currentModalSlider = sliderType;
    currentModalIndex = index;

    let imagesArray = sliderType === 'about' ? aboutImages : sliderType === 'documents' ? documentsImages : accreditationImages;

    modalImage.src = imagesArray[index].src || '';
    modalImage.alt = imagesArray[index].alt || `${sliderType} image ${index + 1}`;

    imageModal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
  }

  closeModal.addEventListener('click', () => {
    imageModal.classList.add('hidden');
    document.body.style.overflow = '';
  });

  function updateModalImage(step) {
    let imagesArray = currentModalSlider === 'about' ? aboutImages : currentModalSlider === 'documents' ? documentsImages : accreditationImages;
    currentModalIndex = (currentModalIndex + step + imagesArray.length) % imagesArray.length;
    modalImage.src = imagesArray[currentModalIndex].src || '';
    modalImage.alt = imagesArray[currentModalIndex].alt || `${currentModalSlider} image ${currentModalIndex + 1}`;
  }

  modalPrev.addEventListener('click', () => updateModalImage(-1));
  modalNext.addEventListener('click', () => updateModalImage(1));

  imageModal.addEventListener('click', (e) => {
    if (e.target === imageModal) {
      imageModal.classList.add('hidden');
      document.body.style.overflow = '';
    }
  });
});
</script>



</body>
</html>






