# AL-Zakhrof Factory Website

This website uses Tailwind CSS for styling in a production-ready setup.

## Setup Instructions

### Prerequisites
- Node.js and npm installed on your system

### Installation
1. Run `npm install` to install dependencies
2. Run `npm run build-css-prod` to build the CSS file

### Development
- Run `dev.bat` (Windows) to start development mode with auto-rebuild
- Or run `npm run build-css` for watch mode
- Make changes to `src/input.css` for custom styles
- Tailwind classes are automatically detected from HTML files

### Production Build
- Run `build.bat` (Windows) for a quick production build
- Or run `npm run build-css-prod` for minified CSS

### File Structure
```
├── src/
│   └── input.css          # Source CSS file with Tailwind directives
├── dist/
│   └── output.css         # Generated CSS file (used by HTML)
├── tailwind.config.js     # Tailwind configuration
├── package.json           # Node.js dependencies
└── *.html                 # HTML files
```

### Customization
- Edit `tailwind.config.js` to customize Tailwind settings
- Add custom CSS to `src/input.css`
- The build process will automatically include only the CSS classes you actually use

## Benefits of This Setup
- ✅ Production-ready (no CDN warnings)
- ✅ Optimized file size (only includes used classes)
- ✅ Custom styling support
- ✅ Fast build times
- ✅ Easy to maintain and update
